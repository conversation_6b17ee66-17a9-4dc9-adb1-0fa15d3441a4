<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Maintenance - Redesigned Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .font-weight-medium { font-weight: 500; }
        .font-weight-semibold { font-weight: 600; }
        .font-weight-bold { font-weight: 700; }
        .typography-heading-3 { font-size: 1.5rem; line-height: 1.2; }
        .typography-body { font-size: 0.875rem; line-height: 1.5; }
        .typography-body-sm { font-size: 0.75rem; line-height: 1.5; }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header Section -->
    <div class="bg-white border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <!-- Page Header -->
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="typography-heading-3 text-gray-900 font-weight-bold">Code Maintenance</h1>
                    <p class="typography-body-sm text-gray-600 mt-1">
                        Select repositories and perform maintenance tasks to keep your codebase optimized
                    </p>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                    <!-- Select All Button -->
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 typography-body-sm font-weight-medium transition-all duration-200 shadow-sm">
                        <div class="w-4 h-4 flex items-center justify-center border-2 border-gray-400 rounded">
                            <svg width="12" height="12" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.5 3L4 7.5L1.5 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </div>
                        <span class="text-gray-700">Select All</span>
                    </button>

                    <!-- Refresh Button -->
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-orange-50 hover:border-orange-300 hover:text-orange-600 typography-body-sm font-weight-medium transition-all duration-200 shadow-sm">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                        <span>Refresh</span>
                    </button>

                    <!-- History Button -->
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-orange-50 hover:border-orange-300 hover:text-orange-600 typography-body-sm font-weight-medium transition-all duration-200 shadow-sm">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                        <span>History</span>
                    </button>

                    <!-- Start Session Button -->
                    <button class="flex items-center gap-2 px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 typography-body-sm font-weight-medium transition-all duration-200 shadow-sm hover:shadow-md">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5,3 19,12 5,21"/>
                        </svg>
                        <span>Start Session</span>
                    </button>
                </div>
            </div>

            <!-- Info Banner -->
            <div class="flex items-start gap-3 bg-gradient-to-r from-orange-50 to-orange-100/50 rounded-lg p-4 border border-orange-200 shadow-sm">
                <div class="text-orange-600 flex-shrink-0 mt-0.5">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 16v-4"/>
                        <path d="M12 8h.01"/>
                    </svg>
                </div>
                <div class="flex-1">
                    <h2 class="text-gray-900 font-weight-semibold typography-body mb-1">About Code Maintenance</h2>
                    <p class="text-gray-700 typography-body-sm leading-relaxed">
                        Maintain and update existing code. Select repositories and perform maintenance tasks to keep your codebase current and optimized.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Section -->
    <div class="flex-1 max-w-7xl mx-auto w-full px-6 py-8">
        <div class="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
            <!-- Repository Card 1 - Selected -->
            <div class="group relative bg-white rounded-xl border-2 border-orange-300 bg-gradient-to-br from-orange-50 to-white shadow-md transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5">
                <!-- Selection Indicator -->
                <div class="absolute -top-1 -right-1 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg width="14" height="14" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.5 3L4 7.5L1.5 5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
                
                <div class="p-4">
                    <!-- Header with Checkbox and Title -->
                    <div class="flex items-start gap-3 mb-3">
                        <input type="checkbox" checked class="w-5 h-5 mt-0.5 text-orange-600 rounded-md border-gray-300 transition-all">
                        <div class="flex-1 min-w-0">
                            <h3 class="typography-body font-weight-semibold text-orange-700 leading-tight mb-1 transition-colors">
                                Workflow-Manager-admin/recipe-creator-266c2ac8
                            </h3>
                            <div class="flex items-center gap-2">
                                <span class="typography-body-sm text-gray-500 capitalize">github</span>
                                <span class="inline-flex items-center px-2 py-1 typography-body-sm rounded-full bg-orange-100 text-orange-700 font-weight-medium">
                                    public
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Repository Details -->
                    <div class="space-y-3">
                        <div>
                            <h4 class="text-gray-700 font-weight-medium typography-body-sm mb-2 flex items-center gap-1">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="6" y1="3" x2="6" y2="15"/>
                                    <circle cx="18" cy="6" r="3"/>
                                    <circle cx="6" cy="18" r="3"/>
                                    <path d="M18 9a9 9 0 0 1-9 9"/>
                                </svg>
                                Repository Details
                            </h4>
                            
                            <div class="space-y-2">
                                <div class="flex items-center gap-2 typography-body-sm bg-gray-50 rounded-lg p-3 border">
                                    <span class="flex-1 truncate text-gray-700 font-mono">
                                        github.com/Workflow-Manager-admin/recipe-creator-266c2ac8
                                    </span>
                                    <button class="flex-shrink-0 p-1.5 rounded-md transition-all duration-200 hover:bg-gray-200 text-gray-500 hover:text-gray-700 hover:scale-110">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="flex items-center gap-2 typography-body-sm text-gray-600 px-3">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="6" y1="3" x2="6" y2="15"/>
                                        <circle cx="18" cy="6" r="3"/>
                                        <circle cx="6" cy="18" r="3"/>
                                        <path d="M18 9a9 9 0 0 1-9 9"/>
                                    </svg>
                                    <span class="truncate font-weight-medium">kavla-main</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Repository Card 2 - Unselected -->
            <div class="group relative bg-white rounded-xl border-2 border-gray-200 hover:border-orange-200 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5">
                <div class="p-4">
                    <!-- Header with Checkbox and Title -->
                    <div class="flex items-start gap-3 mb-3">
                        <input type="checkbox" class="w-5 h-5 mt-0.5 text-orange-600 rounded-md border-gray-300 transition-all">
                        <div class="flex-1 min-w-0">
                            <h3 class="typography-body font-weight-semibold text-gray-900 leading-tight mb-1 group-hover:text-orange-700 transition-colors">
                                Workflow-Manager-admin/recipe-creator-bb3dd0bd
                            </h3>
                            <div class="flex items-center gap-2">
                                <span class="typography-body-sm text-gray-500 capitalize">github</span>
                                <span class="inline-flex items-center px-2 py-1 typography-body-sm rounded-full bg-orange-100 text-orange-700 font-weight-medium">
                                    public
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Repository Details -->
                    <div class="space-y-3">
                        <div>
                            <h4 class="text-gray-700 font-weight-medium typography-body-sm mb-2 flex items-center gap-1">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="6" y1="3" x2="6" y2="15"/>
                                    <circle cx="18" cy="6" r="3"/>
                                    <circle cx="6" cy="18" r="3"/>
                                    <path d="M18 9a9 9 0 0 1-9 9"/>
                                </svg>
                                Repository Details
                            </h4>
                            
                            <div class="space-y-2">
                                <div class="flex items-center gap-2 typography-body-sm bg-gray-50 rounded-lg p-3 border">
                                    <span class="flex-1 truncate text-gray-700 font-mono">
                                        github.com/Workflow-Manager-admin/recipe-creator-bb3dd0bd
                                    </span>
                                    <button class="flex-shrink-0 p-1.5 rounded-md transition-all duration-200 hover:bg-gray-200 text-gray-500 hover:text-gray-700 hover:scale-110">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="flex items-center gap-2 typography-body-sm text-gray-600 px-3">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="6" y1="3" x2="6" y2="15"/>
                                        <circle cx="18" cy="6" r="3"/>
                                        <circle cx="6" cy="18" r="3"/>
                                        <path d="M18 9a9 9 0 0 1-9 9"/>
                                    </svg>
                                    <span class="truncate font-weight-medium">kavla-main</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional placeholder cards -->
            <div class="group relative bg-white rounded-xl border-2 border-gray-200 hover:border-orange-200 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 opacity-60">
                <div class="p-4">
                    <div class="flex items-start gap-3 mb-3">
                        <input type="checkbox" class="w-5 h-5 mt-0.5 text-orange-600 rounded-md border-gray-300 transition-all">
                        <div class="flex-1 min-w-0">
                            <h3 class="typography-body font-weight-semibold text-gray-900 leading-tight mb-1">
                                Example-Repository-3
                            </h3>
                            <div class="flex items-center gap-2">
                                <span class="typography-body-sm text-gray-500 capitalize">github</span>
                                <span class="inline-flex items-center px-2 py-1 typography-body-sm rounded-full bg-orange-100 text-orange-700 font-weight-medium">
                                    private
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <h4 class="text-gray-700 font-weight-medium typography-body-sm mb-2 flex items-center gap-1">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="6" y1="3" x2="6" y2="15"/>
                                    <circle cx="18" cy="6" r="3"/>
                                    <circle cx="6" cy="18" r="3"/>
                                    <path d="M18 9a9 9 0 0 1-9 9"/>
                                </svg>
                                Repository Details
                            </h4>
                            <div class="space-y-2">
                                <div class="flex items-center gap-2 typography-body-sm bg-gray-50 rounded-lg p-3 border">
                                    <span class="flex-1 truncate text-gray-700 font-mono">
                                        github.com/example/repository-3
                                    </span>
                                    <button class="flex-shrink-0 p-1.5 rounded-md transition-all duration-200 hover:bg-gray-200 text-gray-500 hover:text-gray-700 hover:scale-110">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                            <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="flex items-center gap-2 typography-body-sm text-gray-600 px-3">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="6" y1="3" x2="6" y2="15"/>
                                        <circle cx="18" cy="6" r="3"/>
                                        <circle cx="6" cy="18" r="3"/>
                                        <path d="M18 9a9 9 0 0 1-9 9"/>
                                    </svg>
                                    <span class="truncate font-weight-medium">main</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
